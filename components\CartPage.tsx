import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useCart } from '../contexts/CartContext';
import { useAuth } from '../contexts/AuthContext';
import { TrashIcon, PlusIcon, MinusIcon, ShoppingBagIcon, MapPinIcon, ChevronDownIcon } from './common/Icon';
import { useAddress } from '../contexts/AddressContext';
import AddressList from './addresses/AddressList';

interface GroupedCartItem {
  id: string; // Use the first cart item's ID as the group ID
  product: any;
  variant_id?: string;
  cartItemIds: string[]; // Array of all cart item IDs in this group
  totalQuantity: number;
  index?: number;
}

const CartPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { cartItems, cartSummary, loading, removeFromCart, updateQuantity } = useCart();
  const { addresses } = useAddress();
  const [visibleItems, setVisibleItems] = useState<Set<number>>(new Set());
  const [processingItems, setProcessingItems] = useState<Set<string>>(new Set());
  const [selectedAddressId, setSelectedAddressId] = useState<string | null>(null);

  // Mobile enhancement states
  const [isStickyBottomVisible, setIsStickyBottomVisible] = useState(true);
  const [isOrderSummaryInView, setIsOrderSummaryInView] = useState(false);
  const orderSummaryRef = useRef<HTMLDivElement>(null);

  // Helper function to get variant image path
  const getVariantImage = (productName: string, variantId: string) => {
    // Handle hand wash variants specifically
    if (productName.includes('Hand Wash')) {
      switch (variantId) {
        case 'green-apple': return '/assets/products/handwash-1.jpg';
        case 'strawberry': return '/assets/products/handwash-2.jpg';
        case 'lavender': return '/assets/products/handwash-3.jpg';
        default: return '/assets/products/handwash-three-flavors.jpg';
      }
    }
    // Add mappings for other products as needed
    return '';
  };

  // Helper function to group cart items by product and variant
  const groupCartItems = (items: typeof cartItems): GroupedCartItem[] => {
    const groups: { [key: string]: GroupedCartItem } = {};

    // Sort items by ID to ensure consistent grouping
    const sortedItems = [...items].sort((a, b) => a.id.localeCompare(b.id));

    sortedItems.forEach((item) => {
      if (!item.product) return;

      const key = `${item.product.id}-${item.variant_id || 'default'}`;

      if (!groups[key]) {
        groups[key] = {
          id: item.id,
          product: item.product,
          variant_id: item.variant_id,
          cartItemIds: [item.id],
          totalQuantity: item.quantity,
        };
      } else {
        groups[key].cartItemIds.push(item.id);
        groups[key].totalQuantity += item.quantity;
      }
    });

    return Object.values(groups);
  };

  // Redirect if not logged in
  useEffect(() => {
    if (!user && !loading) {
      navigate('/login');
    }
  }, [user, loading, navigate]);

  // Animate items on load
  useEffect(() => {
    const groupedItems = groupCartItems(cartItems);
    const timer = setTimeout(() => {
      groupedItems.forEach((_, index) => {
        setTimeout(() => {
          setVisibleItems(prev => new Set([...prev, index]));
        }, index * 100);
      });
    }, 200);

    return () => clearTimeout(timer);
  }, [cartItems]);

  // Intersection Observer for Order Summary visibility (Mobile Enhancement)
  useEffect(() => {
    if (!orderSummaryRef.current || cartItems.length === 0) {
      return;
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsOrderSummaryInView(entry.isIntersecting);
        // Hide sticky bottom when order summary is in view on mobile/tablet
        if (window.innerWidth < 1024) {
          setIsStickyBottomVisible(!entry.isIntersecting);
        }
      },
      {
        threshold: 0.3,
        rootMargin: '-80px 0px -80px 0px',
      }
    );

    observer.observe(orderSummaryRef.current);
    return () => observer.disconnect();
  }, [cartItems.length]);

  // Handle window resize to ensure proper sticky bottom behavior
  useEffect(() => {
    const handleResize = () => {
      // Reset sticky bottom visibility on desktop
      if (window.innerWidth >= 1024) {
        setIsStickyBottomVisible(false);
      } else if (cartItems.length > 0) {
        setIsStickyBottomVisible(!isOrderSummaryInView);
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize(); // Call once on mount

    return () => window.removeEventListener('resize', handleResize);
  }, [cartItems.length, isOrderSummaryInView]);

  // Handle smooth scroll to order summary
  const handleScrollToOrderSummary = () => {
    if (orderSummaryRef.current) {
      orderSummaryRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest'
      });
    }
  };

  // Handlers for grouped items
  const handleGroupedQuantityChange = async (groupedItem: GroupedCartItem, change: number) => {
    const newQuantity = groupedItem.totalQuantity + change;

    if (newQuantity <= 0) {
      const allItemIds = groupedItem.cartItemIds;
      setProcessingItems(prev => new Set([...prev, ...allItemIds]));

      try {
        await Promise.all(allItemIds.map(id => removeFromCart(id)));
      } finally {
        setProcessingItems(prev => {
          const newSet = new Set(prev);
          allItemIds.forEach(id => newSet.delete(id));
          return newSet;
        });
      }
      return;
    }

    // Only update the first item and handle duplicates after success
    const [firstItemId, ...otherItemIds] = groupedItem.cartItemIds;
    
    // Only mark the first item as processing initially
    setProcessingItems(prev => new Set([...prev, firstItemId]));

    try {
      // Update quantity of the first item
      const result = await updateQuantity(firstItemId, newQuantity);
      
      if (result.success && otherItemIds.length > 0) {
        // Mark remaining items as processing only after successful quantity update
        setProcessingItems(prev => new Set([...prev, ...otherItemIds]));
        // Remove duplicates in parallel
        await Promise.all(otherItemIds.map(id => removeFromCart(id)));
      }
    } finally {
      setProcessingItems(prev => {
        const newSet = new Set(prev);
        groupedItem.cartItemIds.forEach(id => newSet.delete(id));
        return newSet;
      });
    }
  };

  const handleRemoveGroup = async (groupedItem: GroupedCartItem) => {
    const allItemIds = groupedItem.cartItemIds;
    setProcessingItems(prev => new Set([...prev, ...allItemIds]));

    for (const itemId of allItemIds) {
      await removeFromCart(itemId);
    }

    setProcessingItems(prev => {
      const newSet = new Set(prev);
      allItemIds.forEach(id => newSet.delete(id));
      return newSet;
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-page-bg-light to-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-accent-teal mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your cart...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen font-sans relative overflow-hidden">
      {/* Background remains the same... */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-50 via-page-bg-light to-gray-100">
        {/* Previous background patterns remain unchanged */}
      </div>

      <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 py-12 pb-24 lg:pb-12">
        {/* Header remains the same... */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Shopping Cart
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Review your selected items and proceed to checkout
          </p>
        </div>

        {cartItems.length === 0 ? (
          // Empty Cart State
          <div className="text-center py-16">
            <div className="bg-white/90 backdrop-blur-sm rounded-3xl shadow-xl border border-white/50 p-12 max-w-md mx-auto">
              <div className="w-24 h-24 bg-gradient-to-br from-brand-accent-teal/20 to-brand-main-red/20 rounded-full flex items-center justify-center mx-auto mb-6">
                <ShoppingBagIcon size={40} className="text-gray-400" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Your cart is empty</h3>
              <p className="text-gray-600 mb-8">
                Discover our amazing products and add them to your cart
              </p>
              <button
                onClick={() => navigate('/store')}
                className="bg-gradient-to-r from-brand-accent-teal to-brand-accent-teal-darker text-white px-8 py-3 rounded-full font-semibold hover:shadow-lg transform hover:scale-105 transition-all duration-300"
              >
                Continue Shopping
              </button>
            </div>
          </div>
        ) : (
          // Updated Layout to include Address Selection
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
            {/* Cart Items List - Made slightly smaller */}
            <div className="lg:col-span-7 space-y-3 sm:space-y-4 lg:space-y-6">
              {groupCartItems(cartItems).map((groupedItem, index) => {
                const product = groupedItem.product;
                if (!product) return null;

                const isVisible = visibleItems.has(index);
                const isProcessing = groupedItem.cartItemIds.some(id => processingItems.has(id));

                return (
                  <div
                    key={`${product.id}-${groupedItem.variant_id || 'default'}`}
                    className={`bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg border border-white/50 p-3 sm:p-4 lg:p-6 transition-all duration-700 ${
                      isVisible ? 'opacity-100 transform-none' : 'opacity-0 translate-y-8'
                    } ${isProcessing ? 'opacity-50' : ''}`}
                  >
                    <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 lg:gap-6">
                      {/* Product Image */}
                      <div className="flex-shrink-0">
                        <div className="w-20 h-20 sm:w-24 sm:h-24 lg:w-32 lg:h-32 bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg sm:rounded-xl overflow-hidden">
                          {product.name.includes('Hand Wash') && groupedItem.variant_id ? (
                            <img
                              src={getVariantImage(product.name, groupedItem.variant_id)}
                              alt={product.name}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <img
                              src={product.image_url}
                              alt={product.name}
                              className="w-full h-full object-cover"
                            />
                          )}
                        </div>
                      </div>

                      {/* Product Details */}
                      <div className="flex-grow">
                        <div className="flex justify-between items-start mb-2 sm:mb-3 lg:mb-4">
                          <div className="flex-1 min-w-0">
                            <h3 className="text-base sm:text-lg lg:text-xl font-bold text-gray-900 mb-1 sm:mb-2 leading-tight">
                              {product.name}
                              {groupedItem.variant_id && product.variants && (
                                <span className="text-brand-accent-teal block text-sm sm:text-base font-normal mt-1">
                                  {product.variants.find((v: { id: string; name: string }) => v.id === groupedItem.variant_id)?.name}
                                </span>
                              )}
                            </h3>
                            <p className="text-gray-600 text-xs sm:text-sm mb-1 sm:mb-2">{product.category}</p>
                            {product.original_price && product.discount ? (
                              <div className="flex flex-col">
                                <div className="flex items-center space-x-1 sm:space-x-2 mb-1">
                                  <span className="bg-brand-main-red text-white text-xs px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-full font-bold">
                                    {product.discount}
                                  </span>
                                  <span className="text-xs sm:text-sm text-gray-500 line-through">
                                    AED {product.original_price.toFixed(2)}
                                  </span>
                                </div>
                                <p className="text-brand-accent-teal font-semibold text-base sm:text-lg">
                                  AED {product.price.toFixed(2)}
                                </p>
                                <span className="text-xs text-brand-accent-teal font-medium">
                                  Save AED {(product.original_price - product.price).toFixed(2)} each
                                </span>
                              </div>
                            ) : (
                              <p className="text-brand-accent-teal font-semibold text-base sm:text-lg">
                                AED {product.price.toFixed(2)}
                              </p>
                            )}
                          </div>
                          <button
                            onClick={() => handleRemoveGroup(groupedItem)}
                            disabled={isProcessing}
                            className="p-1.5 sm:p-2 text-red-500 hover:bg-red-50 rounded-full transition-colors duration-200 disabled:opacity-50 flex-shrink-0"
                            aria-label="Remove item"
                          >
                            <TrashIcon size={16} className="sm:w-5 sm:h-5" />
                          </button>
                        </div>

                        {/* Quantity Controls */}
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-0">
                          <div className="flex items-center space-x-2 sm:space-x-3">
                            <span className="text-gray-600 font-medium text-sm sm:text-base">Qty:</span>
                            <div className="flex items-center space-x-1 sm:space-x-2">
                              <button
                                onClick={() => handleGroupedQuantityChange(groupedItem, -1)}
                                disabled={isProcessing || groupedItem.totalQuantity <= 1}
                                className="w-7 h-7 sm:w-8 sm:h-8 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                              >
                                <MinusIcon size={14} className="sm:w-4 sm:h-4" />
                              </button>
                              <span className="w-8 sm:w-12 text-center font-semibold text-base sm:text-lg">
                                {groupedItem.totalQuantity}
                              </span>
                              <button
                                onClick={() => handleGroupedQuantityChange(groupedItem, 1)}
                                disabled={isProcessing}
                                className="w-7 h-7 sm:w-8 sm:h-8 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors duration-200 disabled:opacity-50"
                              >
                                <PlusIcon size={14} className="sm:w-4 sm:h-4" />
                              </button>
                            </div>
                          </div>
                          <div className="text-right sm:text-right">
                            {product.original_price && (
                              <p className="text-xs sm:text-sm text-gray-500 line-through">
                                AED {(product.original_price * groupedItem.totalQuantity).toFixed(2)}
                              </p>
                            )}
                            <p className="text-base sm:text-lg font-bold text-gray-900">
                              AED {(product.price * groupedItem.totalQuantity).toFixed(2)}
                            </p>
                            {product.original_price && (
                              <p className="text-xs sm:text-sm text-brand-accent-teal font-semibold">
                                Save AED {((product.original_price - product.price) * groupedItem.totalQuantity).toFixed(2)}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Address Selection - New section */}
            <div className="lg:col-span-5">
              {/* Order Summary */}
              <div ref={orderSummaryRef} className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-white/50 p-6 sticky top-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Order Summary</h3>
                
                <div className="space-y-4 mb-6">
                  {/* Show original total if there are savings */}
                  {cartSummary.totalSavings && cartSummary.totalSavings > 0 && (
                    <div className="flex justify-between">
                      <span className="text-gray-500">Original Total</span>
                      <span className="text-gray-500 line-through">AED {cartSummary.originalTotal?.toFixed(2)}</span>
                    </div>
                  )}

                  {/* Show grouped products with total quantities */}
                  <div className="space-y-2">
                    {Object.values(cartItems.reduce((acc: any, item) => {
                      const product = item.product;
                      if (!product) return acc;
                      
                      const key = `${product.id}-${item.variant_id || 'default'}`;
                      if (!acc[key]) {
                        acc[key] = {
                          product,
                          variant_id: item.variant_id,
                          totalQuantity: 0,
                          totalPrice: 0
                        };
                      }
                      acc[key].totalQuantity += item.quantity;
                      acc[key].totalPrice += product.price * item.quantity;
                      return acc;
                    }, {})).map((groupedItem: any) => (
                      <div key={`${groupedItem.product.id}-${groupedItem.variant_id || 'default'}`} 
                           className="flex justify-between items-start text-sm gap-2">
                        <span className="text-gray-600 flex-1 leading-relaxed">
                          <span className="block sm:inline">{groupedItem.product.name}</span>
                          {groupedItem.variant_id && groupedItem.product.variants && (
                            <span className="block text-brand-accent-teal text-sm">
                              {groupedItem.product.variants.find((v: { id: string; name: string }) => v.id === groupedItem.variant_id)?.name}
                            </span>
                          )}
                          <span className="block sm:inline sm:ml-1">x {groupedItem.totalQuantity}</span>
                        </span>
                        <span className="font-semibold text-right flex-shrink-0">
                          AED {groupedItem.totalPrice.toFixed(2)}
                        </span>
                      </div>
                    ))}
                  </div>

                  {/* Show total savings */}
                  {cartSummary.totalSavings && cartSummary.totalSavings > 0 && (
                    <div className="flex justify-between border-t pt-2">
                      <span className="text-brand-accent-teal font-semibold">Total Savings</span>
                      <span className="text-brand-accent-teal font-bold">-AED {cartSummary.totalSavings.toFixed(2)}</span>
                    </div>
                  )}

                  <div className="border-t pt-4">
                    <div className="flex justify-between text-xl font-bold">
                      <span>Total</span>
                      <span className="text-brand-main-red">AED {cartSummary.total.toFixed(2)}</span>
                    </div>

                    {/* Highlight total savings */}
                    {cartSummary.totalSavings && cartSummary.totalSavings > 0 && (
                      <div className="mt-2 text-center">
                        <span className="bg-brand-accent-teal text-white px-4 py-2 rounded-full text-sm font-bold">
                          🎉 You saved AED {cartSummary.totalSavings.toFixed(2)} total!
                        </span>
                      </div>
                    )}
                  </div>
                </div>
                
                {/* Updated checkout button section */}
                <button
                  className={`w-full bg-gradient-to-r from-brand-main-red to-brand-main-red-darker text-white py-4 rounded-full font-bold text-lg transition-all duration-300 mb-4 ${
                    selectedAddressId ? 'hover:shadow-lg transform hover:scale-105' : 'opacity-50 cursor-not-allowed'
                  }`}
                  disabled={!selectedAddressId}
                  onClick={() => {
                    if (!selectedAddressId) {
                      return;
                    }
                    // Create bill-like message
                    let message = "I'd like to place an order:\n\n";
                    message += "==============================\n";
                    message += "          ORDER SUMMARY        \n";
                    message += "==============================\n\n";

                    // Add delivery address
                    const deliveryAddress = addresses.find(addr => addr.id === selectedAddressId);
                    if (deliveryAddress) {
                      message += "DELIVERY ADDRESS:\n";
                      message += `${deliveryAddress.full_name}\n`;
                      message += `${deliveryAddress.phone_number}\n`;
                      message += `${deliveryAddress.street_address}\n`;
                      if (deliveryAddress.apartment) {
                        message += `${deliveryAddress.apartment}\n`;
                      }
                      message += `${deliveryAddress.city}, ${deliveryAddress.state} ${deliveryAddress.postal_code}\n`;
                      message += `${deliveryAddress.country}\n\n`;
                      message += "------------------------------\n\n";
                    }

                    message += "ITEMS:\n";
                    
                    cartItems.forEach(item => {
                      const product = item.product;
                      if (!product) return;
                      
                      let productText = `• ${product.name}`;
                      
                      if (item.variant_id && product.variants) {
                        const variant = product.variants.find(v => v.id === item.variant_id);
                        if (variant) {
                          productText += ` (${variant.name})`;
                        }
                      }
                      
                      productText += ` - ${item.quantity} x AED ${product.price.toFixed(2)}`;
                      const lineTotal = product.price * item.quantity;
                      productText += ` = AED ${lineTotal.toFixed(2)}\n`;
                      
                      message += productText;
                    });
                    
                    message += "\n------------------------------\n";
                    
                    // Add savings if available
                    if (cartSummary.totalSavings && cartSummary.totalSavings > 0) {
                      message += `Subtotal: AED ${cartSummary.originalTotal?.toFixed(2)}\n`;
                      message += `Savings: -AED ${cartSummary.totalSavings.toFixed(2)}\n`;
                    }
                    
                    message += `TOTAL: AED ${cartSummary.total.toFixed(2)}\n\n`;
                    message += "Please confirm this order. Thank you!";
                    
                    const whatsappUrl = `https://wa.me/919778248100?text=${encodeURIComponent(message)}`;
                    window.open(whatsappUrl, '_blank');
                  }}
                >
                  Proceed to Checkout via WhatsApp
                </button>

                {!selectedAddressId && (
                  <div className="text-sm text-brand-main-red mb-4 text-center">
                    Please select a delivery address below to proceed with checkout
                  </div>
                )}

                <div className="text-sm text-gray-600 italic mb-4">
                  Payments will be done via WhatsApp and you will be redirected to WhatsApp.
                </div>

                <button
                  onClick={() => navigate('/store')}
                  className="w-full bg-transparent border-2 border-brand-accent-teal text-brand-accent-teal py-3 rounded-full font-semibold hover:bg-brand-accent-teal hover:text-white transition-all duration-300"
                >
                  Continue Shopping
                </button>
              </div>

              {/* Address Selection */}
              <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-white/50 p-6 mt-6">
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Select Delivery Address</h3>
                <AddressList
                  onSelect={true}
                  onAddressSelect={setSelectedAddressId}
                  selectedAddressId={selectedAddressId}
                />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Mobile & Tablet Sticky Bottom Section */}
      {cartItems.length > 0 && (
        <div
          className={`fixed bottom-0 left-0 right-0 z-50 lg:hidden transition-all duration-500 ease-out transform ${
            isStickyBottomVisible && !isOrderSummaryInView
              ? 'opacity-100 translate-y-0 scale-100'
              : 'opacity-0 translate-y-full scale-95 pointer-events-none'
          }`}
          style={{
            backdropFilter: 'blur(20px)',
            WebkitBackdropFilter: 'blur(20px)',
          }}
        >
          {/* Background with glassmorphism */}
          <div className="bg-white/95 backdrop-blur-xl border-t border-gray-200/50 shadow-2xl">
            <div className="container mx-auto px-4 py-4">
              <div className="flex items-center justify-between">
                {/* Total Amount */}
                <div className="flex-1">
                  <p className="text-sm text-gray-600 mb-1">Total Amount</p>
                  <p className="text-xl font-bold text-brand-main-red">
                    AED {cartSummary.total.toFixed(2)}
                  </p>
                  {cartSummary.totalSavings && cartSummary.totalSavings > 0 && (
                    <p className="text-sm text-brand-accent-teal font-semibold">
                      Save AED {cartSummary.totalSavings.toFixed(2)}
                    </p>
                  )}
                </div>

                {/* Proceed to Checkout Button with Down Arrow */}
                <button
                  onClick={handleScrollToOrderSummary}
                  className="group relative bg-gradient-to-r from-brand-accent-teal to-brand-accent-teal-darker text-white font-bold py-4 px-6 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-brand-accent-teal/30 min-h-[44px] min-w-[44px] flex items-center space-x-3"
                  aria-label="Proceed to checkout - scroll to order summary"
                >
                  {/* Shimmer Effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl"></div>

                  <span className="relative z-10 text-base font-semibold">Proceed to Checkout</span>

                  {/* Professional Down Arrow */}
                  <div className="relative z-10 bg-white/20 backdrop-blur-sm rounded-full p-2 group-hover:bg-white/30 transition-all duration-300">
                    <ChevronDownIcon
                      size={20}
                      className="transform group-hover:translate-y-1 transition-transform duration-300"
                    />
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CartPage;
